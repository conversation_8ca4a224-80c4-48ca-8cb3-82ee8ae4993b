"""
手性感知的分子生成脚本
基于训练好的手性感知VAE模型生成对映异构体及其毒性预测
"""

import torch
import numpy as np
import pandas as pd
import pickle
import os
from typing import Dict, List, Tuple, Optional
from rdkit import Chem
from rdkit.Chem import Descriptors

from chiral_model import ChiralToxicityVAE
from chiral_features import ChiralMolecularFeatureExtractor
from chiral_data import ChiralToxicityDataset


class ChiralMoleculeGenerator:
    """手性感知的分子生成器"""
    
    def __init__(self, model_path: str, metadata_path: str):
        """
        初始化生成器
        
        Args:
            model_path: 训练好的模型路径
            metadata_path: 元数据路径
        """
        self.device = torch.device('cpu')  # 强制使用CPU避免CUDA问题
        self.model_path = model_path
        self.metadata_path = metadata_path
        
        # 加载模型和元数据
        self._load_model_and_metadata()
        
        # 创建特征提取器
        self.feature_extractor = ChiralMolecularFeatureExtractor()
        

    
    def _load_model_and_metadata(self):
        """加载模型和元数据"""
        # 加载检查点
        checkpoint = torch.load(self.model_path, map_location=self.device)
        model_config = checkpoint['config']

        # 从检查点的权重推断正确的维度
        state_dict = checkpoint['model_state_dict']

        # 从encoder.0.weight推断输入维度 (molecular_feature_dim + toxicity_dim)
        encoder_input_dim = state_dict['encoder.0.weight'].shape[1]

        # 从decoder最后一层推断toxicity_dim
        decoder_keys = [k for k in state_dict.keys() if k.startswith('decoder.') and k.endswith('.weight')]
        last_decoder_key = max(decoder_keys, key=lambda x: int(x.split('.')[1]))
        toxicity_dim = state_dict[last_decoder_key].shape[0]

        # 计算molecular_feature_dim
        molecular_feature_dim = encoder_input_dim - toxicity_dim

        print(f"从检查点推断的维度: molecular_feature_dim={molecular_feature_dim}, toxicity_dim={toxicity_dim}")

        # 创建模型
        self.model = ChiralToxicityVAE(
            molecular_feature_dim=molecular_feature_dim,
            toxicity_dim=toxicity_dim,
            latent_dim=model_config['latent_dim'],
            hidden_dims=model_config['hidden_dims'],
            chiral_feature_indices=model_config.get('chiral_feature_indices', None)
        ).to(self.device)

        # 加载模型权重
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        # 加载元数据
        with open(self.metadata_path, 'rb') as f:
            self.metadata = pickle.load(f)
        
        self.feature_names = self.metadata['feature_names']
        self.toxicity_endpoints = self.metadata['toxicity_endpoints']
        self.scaler = self.metadata['scaler']
        

    
    def generate_enantiomer_pair_from_smiles(self, smiles: str, 
                                           n_samples: int = 1,
                                           temperature: float = 1.0) -> Dict:
        """
        从给定SMILES生成对映异构体对的毒性预测
        
        Args:
            smiles: 输入的SMILES字符串
            n_samples: 生成样本数
            temperature: 生成温度
            
        Returns:
            包含对映异构体毒性预测的字典
        """
        # 提取分子特征
        features = self.feature_extractor.extract_features(smiles)
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        features_tensor = torch.FloatTensor(features_scaled).to(self.device)
        
        # 生成对映异构体SMILES
        r_smiles, s_smiles = self._generate_enantiomer_smiles(smiles)
        
        # 生成毒性预测
        with torch.no_grad():
            toxicity_r, toxicity_s = self.model.generate_enantiomer_pair(
                features_tensor, n_samples=n_samples, temperature=temperature
            )
        
        # 转换为numpy数组
        toxicity_r = toxicity_r.cpu().numpy()
        toxicity_s = toxicity_s.cpu().numpy()
        
        # 计算差异
        toxicity_diff = np.abs(toxicity_r - toxicity_s)
        
        return {
            'original_smiles': smiles,
            'r_enantiomer_smiles': r_smiles,
            's_enantiomer_smiles': s_smiles,
            'r_toxicity_probs': toxicity_r,
            's_toxicity_probs': toxicity_s,
            'toxicity_differences': toxicity_diff,
            'r_toxicity_binary': (toxicity_r > 0.5).astype(int),
            's_toxicity_binary': (toxicity_s > 0.5).astype(int),
            'molecular_features': features,
            'toxicity_endpoints': self.toxicity_endpoints
        }
    
    def _generate_enantiomer_smiles(self, smiles: str) -> Tuple[str, str]:
        """生成对映异构体SMILES对"""
        # 检查是否包含手性中心
        if '@' not in smiles:
            # 如果没有手性中心，返回相同的SMILES
            return smiles, smiles
        
        # 生成对映异构体
        enantiomer = smiles
        
        # 翻转手性中心
        temp_marker = "TEMP_MARKER"
        enantiomer = enantiomer.replace("@@H", temp_marker)
        enantiomer = enantiomer.replace("@H", "@@H")
        enantiomer = enantiomer.replace(temp_marker, "@H")
        
        # 处理其他手性标记
        if "@H" not in enantiomer:
            enantiomer = enantiomer.replace("@@]", "TEMP_BRACKET")
            enantiomer = enantiomer.replace("@]", "@@]")
            enantiomer = enantiomer.replace("TEMP_BRACKET", "@]")
            
            enantiomer = enantiomer.replace("@@)", "TEMP_PAREN")
            enantiomer = enantiomer.replace("@)", "@@)")
            enantiomer = enantiomer.replace("TEMP_PAREN", "@)")
        
        # 确定哪个是R，哪个是S（简化处理）
        if smiles.count('@') > smiles.count('@@'):
            return smiles, enantiomer  # 原始为R，生成的为S
        else:
            return enantiomer, smiles  # 生成的为R，原始为S
    
    def generate_diverse_enantiomers(self, base_smiles_list: List[str],
                                   n_samples_per_molecule: int = 5,
                                   temperature: float = 1.0) -> pd.DataFrame:
        """
        为多个分子生成多样化的对映异构体毒性预测
        
        Args:
            base_smiles_list: 基础分子SMILES列表
            n_samples_per_molecule: 每个分子的生成样本数
            temperature: 生成温度
            
        Returns:
            包含所有生成结果的DataFrame
        """
        all_results = []
        
        for i, smiles in enumerate(base_smiles_list):
            try:
                # 生成对映异构体对
                result = self.generate_enantiomer_pair_from_smiles(
                    smiles, n_samples=n_samples_per_molecule, temperature=temperature
                )
                
                # 为每个样本创建记录
                for sample_idx in range(n_samples_per_molecule):
                    # R对映异构体
                    r_record = {
                        'molecule_id': f"mol_{i:04d}",
                        'sample_id': f"sample_{sample_idx:02d}",
                        'enantiomer_type': 'R',
                        'smiles': result['r_enantiomer_smiles'],
                        'partner_smiles': result['s_enantiomer_smiles'],
                        'is_chiral': '@' in smiles,
                    }
                    
                    # 添加毒性预测
                    for j, endpoint in enumerate(self.toxicity_endpoints):
                        r_record[f'{endpoint}_prob'] = result['r_toxicity_probs'][sample_idx, j]
                        r_record[f'{endpoint}_binary'] = result['r_toxicity_binary'][sample_idx, j]
                    
                    # 添加分子特征
                    for j, feature_name in enumerate(self.feature_names):
                        r_record[f'feature_{feature_name}'] = result['molecular_features'][j]
                    
                    all_results.append(r_record)
                    
                    # S对映异构体
                    s_record = r_record.copy()
                    s_record['enantiomer_type'] = 'S'
                    s_record['smiles'] = result['s_enantiomer_smiles']
                    s_record['partner_smiles'] = result['r_enantiomer_smiles']
                    
                    # 更新毒性预测
                    for j, endpoint in enumerate(self.toxicity_endpoints):
                        s_record[f'{endpoint}_prob'] = result['s_toxicity_probs'][sample_idx, j]
                        s_record[f'{endpoint}_binary'] = result['s_toxicity_binary'][sample_idx, j]
                    
                    all_results.append(s_record)
                
            except Exception as e:
                continue
        
        return pd.DataFrame(all_results)
    
    def analyze_enantiomer_differences(self, results_df: pd.DataFrame) -> Dict:
        """分析对映异构体之间的毒性差异"""
        analysis = {
            'total_pairs': len(results_df) // 2,
            'endpoint_differences': {},
            'significant_differences': {},
            'average_differences': {}
        }
        
        # 按分子ID和样本ID分组
        grouped = results_df.groupby(['molecule_id', 'sample_id'])
        
        for endpoint in self.toxicity_endpoints:
            differences = []
            significant_count = 0
            
            for (mol_id, sample_id), group in grouped:
                if len(group) == 2:  # 确保有R和S两个对映异构体
                    r_prob = group[group['enantiomer_type'] == 'R'][f'{endpoint}_prob'].iloc[0]
                    s_prob = group[group['enantiomer_type'] == 'S'][f'{endpoint}_prob'].iloc[0]
                    
                    diff = abs(r_prob - s_prob)
                    differences.append(diff)
                    
                    if diff > 0.1:  # 显著差异阈值
                        significant_count += 1
            
            analysis['endpoint_differences'][endpoint] = differences
            analysis['significant_differences'][endpoint] = significant_count / len(differences) if differences else 0
            analysis['average_differences'][endpoint] = np.mean(differences) if differences else 0
        
        return analysis
    
    def save_results(self, results_df: pd.DataFrame, output_dir: str = 'chiral_generation_results'):
        """保存生成结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存完整结果
        results_df.to_csv(os.path.join(output_dir, 'enantiomer_predictions.csv'), index=False)
        
        # 分析差异
        analysis = self.analyze_enantiomer_differences(results_df)
        
        # 保存分析结果
        with open(os.path.join(output_dir, 'enantiomer_analysis.pkl'), 'wb') as f:
            pickle.dump(analysis, f)
        
        # 创建摘要报告
        summary = {
            'total_molecules': results_df['molecule_id'].nunique(),
            'total_enantiomer_pairs': analysis['total_pairs'],
            'average_differences_by_endpoint': analysis['average_differences'],
            'significant_difference_ratios': analysis['significant_differences']
        }
        
        with open(os.path.join(output_dir, 'generation_summary.txt'), 'w') as f:
            f.write("手性感知分子生成结果摘要\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"总分子数: {summary['total_molecules']}\n")
            f.write(f"对映异构体对数: {summary['total_enantiomer_pairs']}\n\n")
            f.write("各终点平均毒性差异:\n")
            for endpoint, diff in summary['average_differences_by_endpoint'].items():
                f.write(f"  {endpoint}: {diff:.4f}\n")
            f.write("\n显著差异比例 (>0.1):\n")
            for endpoint, ratio in summary['significant_difference_ratios'].items():
                f.write(f"  {endpoint}: {ratio:.2%}\n")
        
        return output_dir

    def generate_novel_chiral_molecules(self, n_molecules: int = 1000,
                                       temperature: float = 1.0,
                                       ensure_chirality: bool = True,
                                       generate_pairs: bool = True) -> List[Dict]:
        """
        生成全新的手性分子结构和毒性标签

        Args:
            n_molecules: 要生成的分子数量
            temperature: 生成温度，控制多样性
            ensure_chirality: 是否确保生成手性分子
            generate_pairs: 是否生成对映异构体对

        Returns:
            生成的分子数据列表
        """
        generated_molecules = []

        # 基础手性分子模板
        chiral_templates = [
            "C[C@H](O)C(=O)O",          # 乳酸骨架
            "C[C@H](N)C(=O)O",          # 氨基酸骨架
            "CC[C@H](O)C(=O)O",         # 羟基酸骨架
            "C[C@H](O)[C@H](O)C",       # 双手性中心
            "CC(C)[C@H](N)C(=O)O",      # 支链氨基酸
            "C[C@H](O)c1ccccc1",        # 芳香手性醇
            "C[C@H](N)c1ccccc1",        # 芳香手性胺
            "C[C@H](C(=O)O)c1ccccc1",   # 芳香手性酸
        ]

        # 可替换的官能团（简化为安全的单原子替换）
        functional_groups = [
            "O", "N", "S", "F", "Cl", "Br"
        ]

        valid_count = 0
        chiral_count = 0

        for i in range(n_molecules):
            try:
                # 随机选择模板
                template = np.random.choice(chiral_templates)

                # 生成变体
                novel_smiles = self._generate_molecular_variant(template, functional_groups, temperature)

                # 确保是手性分子
                if ensure_chirality and '@' not in novel_smiles:
                    continue

                chiral_count += 1

                # 验证SMILES有效性
                if not self._validate_smiles(novel_smiles):
                    if i < 10:  # 只打印前10个错误
                        print(f"无效SMILES: {novel_smiles}")
                    continue

                valid_count += 1

                # 生成毒性标签
                toxicity_labels = self._generate_toxicity_labels(novel_smiles, temperature)

                # 创建分子记录
                molecule_data = {
                    'id': f"generated_{i:06d}",
                    'smiles': novel_smiles,
                    'template': template,
                    'toxicity_labels': toxicity_labels,
                    'valid': True,
                    'has_enantiomer': False,
                    'enantiomer_smiles': None,
                    'toxicity_difference': 0.0
                }

                # 生成对映异构体
                if generate_pairs and '@' in novel_smiles:
                    enantiomer_smiles = self._generate_enantiomer(novel_smiles)
                    if enantiomer_smiles != novel_smiles:
                        enantiomer_toxicity = self._generate_enantiomer_toxicity_labels(
                            toxicity_labels, temperature
                        )

                        molecule_data['has_enantiomer'] = True
                        molecule_data['enantiomer_smiles'] = enantiomer_smiles
                        molecule_data['toxicity_difference'] = np.mean(
                            np.abs(np.array(list(toxicity_labels.values())) -
                                  np.array(list(enantiomer_toxicity.values())))
                        )

                        # 添加对映异构体记录
                        enantiomer_data = {
                            'id': f"generated_{i:06d}_enantiomer",
                            'smiles': enantiomer_smiles,
                            'template': template,
                            'toxicity_labels': enantiomer_toxicity,
                            'valid': True,
                            'has_enantiomer': True,
                            'enantiomer_smiles': novel_smiles,
                            'toxicity_difference': molecule_data['toxicity_difference']
                        }

                        generated_molecules.append(enantiomer_data)

                generated_molecules.append(molecule_data)

            except Exception as e:
                if i < 10:  # 只打印前10个异常
                    print(f"生成异常: {e}")
                continue

        print(f"生成统计: 尝试={n_molecules}, 手性={chiral_count}, 有效={valid_count}, 最终={len(generated_molecules)}")
        return generated_molecules

    def _generate_molecular_variant(self, template: str, functional_groups: List[str],
                                   temperature: float) -> str:
        """基于模板生成分子变体"""
        variant = template

        # 随机替换和修改（更保守的方法）
        if np.random.random() < temperature * 0.2:
            # 简单的甲基替换
            if 'C[C@H]' in variant and np.random.random() < 0.5:
                variant = variant.replace('C[C@H]', 'CC[C@H]', 1)
            elif 'C[C@@H]' in variant and np.random.random() < 0.5:
                variant = variant.replace('C[C@@H]', 'CC[C@@H]', 1)

        # 不进行复杂的官能团添加，保持SMILES的有效性
        return variant

    def _validate_smiles(self, smiles: str) -> bool:
        """验证SMILES有效性"""
        try:
            from rdkit import Chem
            mol = Chem.MolFromSmiles(smiles)
            return mol is not None
        except:
            return False

    def _generate_toxicity_labels(self, smiles: str, temperature: float) -> Dict[str, float]:
        """为新分子生成毒性标签"""
        # 基于分子特征生成合理的毒性标签
        features = self.feature_extractor.extract_features(smiles)
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        features_tensor = torch.FloatTensor(features_scaled).to(self.device)

        with torch.no_grad():
            # 使用模型生成毒性预测
            toxicity_probs = self.model.generate_enantiomer_pair(features_tensor, temperature=temperature)[0]
            toxicity_probs = toxicity_probs.cpu().numpy()[0]

        # 转换为标签字典
        toxicity_labels = {}
        for i, endpoint in enumerate(self.toxicity_endpoints):
            toxicity_labels[endpoint] = float(toxicity_probs[i])

        return toxicity_labels

    def _generate_enantiomer_toxicity_labels(self, original_labels: Dict[str, float],
                                           temperature: float) -> Dict[str, float]:
        """为对映异构体生成不同的毒性标签"""
        enantiomer_labels = {}

        # 基于生物学原理调整毒性
        toxicity_modifiers = {
            'FishAT': 0.3,
            'acute_oral_toxicity': 0.7,
            'skin_sensitization': 0.3,
            'eye_irritation': 0.8,
            'skin_irritation': 0.6,
            'mutagenicity': 0.2
        }

        for endpoint, original_value in original_labels.items():
            modifier = toxicity_modifiers.get(endpoint, 0.5)

            if np.random.random() > modifier:
                # 显著差异
                enantiomer_labels[endpoint] = 1.0 - original_value
            else:
                # 轻微差异
                noise = np.random.normal(0, 0.1 * temperature)
                enantiomer_labels[endpoint] = max(0.0, min(1.0, original_value + noise))

        return enantiomer_labels

    def save_generated_molecules(self, generated_data: List[Dict],
                               output_dir: str = 'generated_molecules') -> str:
        """保存生成的分子数据"""
        os.makedirs(output_dir, exist_ok=True)

        # 创建DataFrame
        rows = []
        for mol_data in generated_data:
            row = {
                'id': mol_data['id'],
                'smiles': mol_data['smiles'],
                'template': mol_data['template'],
                'valid': mol_data['valid'],
                'has_enantiomer': mol_data['has_enantiomer'],
                'enantiomer_smiles': mol_data.get('enantiomer_smiles', ''),
                'toxicity_difference': mol_data.get('toxicity_difference', 0.0)
            }

            # 添加毒性标签
            for endpoint, value in mol_data['toxicity_labels'].items():
                row[f'{endpoint}_toxicity'] = value
                row[f'{endpoint}_binary'] = 1 if value > 0.5 else 0

            rows.append(row)

        df = pd.DataFrame(rows)

        # 保存CSV文件
        csv_path = os.path.join(output_dir, 'generated_chiral_molecules.csv')
        df.to_csv(csv_path, index=False)

        # 保存统计信息
        stats = {
            'total_molecules': len(generated_data),
            'chiral_molecules': sum(1 for mol in generated_data if '@' in mol['smiles']),
            'valid_molecules': sum(1 for mol in generated_data if mol['valid']),
            'enantiomer_pairs': sum(1 for mol in generated_data if mol['has_enantiomer']) // 2,
            'avg_toxicity_difference': np.mean([mol['toxicity_difference'] for mol in generated_data if mol['has_enantiomer']])
        }

        with open(os.path.join(output_dir, 'generation_stats.txt'), 'w') as f:
            f.write("手性分子生成统计\n")
            f.write("=" * 30 + "\n")
            for key, value in stats.items():
                f.write(f"{key}: {value}\n")

        print(f"生成统计:")
        print(f"  总分子数: {stats['total_molecules']}")
        print(f"  手性分子: {stats['chiral_molecules']}")
        print(f"  有效分子: {stats['valid_molecules']}")
        print(f"  对映异构体对: {stats['enantiomer_pairs']}")
        print(f"  平均毒性差异: {stats['avg_toxicity_difference']:.4f}")

        return output_dir

    def _generate_enantiomer(self, smiles: str) -> str:
        """生成对映异构体SMILES"""
        if '@' not in smiles:
            return smiles

        # 翻转手性中心
        enantiomer = smiles

        # 处理 @H 和 @@H
        temp_marker = "TEMP_MARKER"
        enantiomer = enantiomer.replace("@@H", temp_marker)
        enantiomer = enantiomer.replace("@H", "@@H")
        enantiomer = enantiomer.replace(temp_marker, "@H")

        # 处理其他手性标记
        if "@H" not in enantiomer:
            enantiomer = enantiomer.replace("@@]", "TEMP_BRACKET")
            enantiomer = enantiomer.replace("@]", "@@]")
            enantiomer = enantiomer.replace("TEMP_BRACKET", "@]")

            enantiomer = enantiomer.replace("@@)", "TEMP_PAREN")
            enantiomer = enantiomer.replace("@)", "@@)")
            enantiomer = enantiomer.replace("TEMP_PAREN", "@)")

        return enantiomer



