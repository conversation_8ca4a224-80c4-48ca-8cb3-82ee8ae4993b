"""
手性感知毒性预测模型 - 主程序
整合训练、生成和测试功能的统一入口
"""

import os
import torch
import pandas as pd
import numpy as np
from typing import Dict, List

from chiral_data import ChiralToxicityDataset, create_train_val_test_split
from chiral_train import ChiralVAETrainer, CHIRAL_CONFIG
from chiral_generate import ChiralMoleculeGenerator
from torch.utils.data import DataLoader


# 配置参数 - 在这里自定义您的设置
CONFIG = {
    # 运行模式: 'train', 'generate', 'evaluate'
    'mode': 'generate',

    # 训练数据文件路径 (训练模式必需)
    'data_file': 'data/Chiral_AquaTox_scr_FAT.csv',

    # 模型文件路径
    'model_path': 'chiral_models/best_chiral_model.pth',

    # 元数据文件路径
    'metadata_path': 'chiral_data/chiral_metadata.pkl',

    # 生成参数
    'n_molecules': 1000,        # 要生成的分子数量
    'temperature': 1.0,         # 生成温度 (控制多样性)
    'ensure_chirality': True,   # 确保生成手性分子
    'generate_pairs': True,     # 生成对映异构体对

    # 输出目录
    'output_dir': 'chiral_models'
}


def train_model(data_file: str, config: Dict = None) -> str:
    """训练手性感知VAE模型"""
    if config is None:
        config = CHIRAL_CONFIG
    
    print("=" * 50)
    print("开始训练手性感知毒性预测模型")
    print("=" * 50)
    
    if not os.path.exists(data_file):
        raise FileNotFoundError(f"数据文件不存在: {data_file}")
    
    # 加载和处理数据
    print("1. 加载和处理数据...")
    dataset = ChiralToxicityDataset(data_file)
    
    # 创建数据增强版本
    print("2. 创建对映异构体数据增强...")
    augmented_dataset = dataset.create_enantiomer_augmented_dataset()
    
    # 分割数据
    train_dataset, val_dataset, test_dataset = create_train_val_test_split(augmented_dataset)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)
    
    print(f"数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}, 测试={len(test_dataset)}")
    
    # 创建训练器
    print("3. 创建模型和训练器...")
    trainer = ChiralVAETrainer(config)
    
    feature_dim = len(dataset.get_feature_names())
    toxicity_dim = len(dataset.toxicity_endpoints)
    
    trainer.create_model(feature_dim, toxicity_dim)
    
    # 保存数据集和元数据
    print("4. 保存数据集和元数据...")
    dataset.save_processed_data('chiral_data')

    # 开始训练
    print("5. 开始训练...")
    trainer.train(train_loader, val_loader)

    model_path = os.path.join(config['output_dir'], 'best_chiral_model.pth')
    print(f"训练完成! 模型已保存到: {model_path}")

    return model_path


def generate_molecules(model_path: str, metadata_path: str,
                      n_molecules: int = 1000, temperature: float = 1.0,
                      ensure_chirality: bool = True, generate_pairs: bool = True,
                      output_dir: str = 'generated_molecules') -> str:
    """生成新的手性分子结构和毒性标签"""
    print("=" * 50)
    print("生成手性分子结构和毒性标签")
    print("=" * 50)

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

    if not os.path.exists(metadata_path):
        raise FileNotFoundError(f"元数据文件不存在: {metadata_path}")

    # 创建生成器
    print("1. 加载训练好的模型...")
    generator = ChiralMoleculeGenerator(model_path, metadata_path)

    # 生成新分子
    print(f"2. 生成 {n_molecules} 个新的手性分子...")
    generated_data = generator.generate_novel_chiral_molecules(
        n_molecules=n_molecules,
        temperature=temperature,
        ensure_chirality=ensure_chirality,
        generate_pairs=generate_pairs
    )

    # 保存结果
    print("3. 保存生成的分子和毒性数据...")
    output_path = generator.save_generated_molecules(generated_data, output_dir)

    print(f"生成完成! 结果已保存到: {output_path}")

    return output_path


def evaluate_model(model_path: str, metadata_path: str) -> Dict:
    """评估模型生成质量"""
    print("=" * 50)
    print("评估模型生成质量")
    print("=" * 50)

    # 创建生成器
    generator = ChiralMoleculeGenerator(model_path, metadata_path)

    # 生成少量分子用于评估
    print("生成测试分子...")
    test_data = generator.generate_novel_chiral_molecules(
        n_molecules=100,
        temperature=1.0,
        ensure_chirality=True,
        generate_pairs=True
    )

    # 评估生成质量
    evaluation = {
        'total_generated': len(test_data),
        'chiral_molecules': sum(1 for mol in test_data if '@' in mol['smiles']),
        'valid_smiles': sum(1 for mol in test_data if mol['valid']),
        'unique_molecules': len(set(mol['smiles'] for mol in test_data)),
        'enantiomer_pairs': sum(1 for mol in test_data if mol['has_enantiomer']),
        'avg_toxicity_difference': np.mean([mol['toxicity_difference'] for mol in test_data if mol['has_enantiomer']])
    }

    print("评估结果:")
    print(f"  总生成数: {evaluation['total_generated']}")
    print(f"  手性分子: {evaluation['chiral_molecules']} ({evaluation['chiral_molecules']/evaluation['total_generated']*100:.1f}%)")
    print(f"  有效SMILES: {evaluation['valid_smiles']} ({evaluation['valid_smiles']/evaluation['total_generated']*100:.1f}%)")
    print(f"  唯一分子: {evaluation['unique_molecules']} ({evaluation['unique_molecules']/evaluation['total_generated']*100:.1f}%)")
    print(f"  对映异构体对: {evaluation['enantiomer_pairs']}")
    print(f"  平均毒性差异: {evaluation['avg_toxicity_difference']:.4f}")

    return evaluation





def run_chiral_model():
    """
    根据配置运行手性感知分子生成模型
    直接在脚本中自定义参数，无需命令行
    """
    print("手性感知分子生成模型")
    print("=" * 50)
    print(f"运行模式: {CONFIG['mode']}")
    print("=" * 50)

    try:
        if CONFIG['mode'] == 'train':
            # 训练模式
            if not CONFIG['data_file']:
                print("错误: 训练模式需要指定数据文件")
                return False

            print(f"数据文件: {CONFIG['data_file']}")
            model_path = train_model(CONFIG['data_file'])
            print(f"\n✅ 训练完成! 模型保存在: {model_path}")
            return True

        elif CONFIG['mode'] == 'generate':
            # 生成模式
            print(f"生成分子数量: {CONFIG['n_molecules']}")
            print(f"生成温度: {CONFIG['temperature']}")
            print(f"确保手性: {CONFIG['ensure_chirality']}")
            print(f"生成对映异构体对: {CONFIG['generate_pairs']}")

            output_path = generate_molecules(
                CONFIG['model_path'],
                CONFIG['metadata_path'],
                CONFIG['n_molecules'],
                CONFIG['temperature'],
                CONFIG['ensure_chirality'],
                CONFIG['generate_pairs'],
                CONFIG['output_dir']
            )
            print(f"\n✅ 生成完成! 结果保存在: {output_path}")
            return True

        elif CONFIG['mode'] == 'evaluate':
            # 评估模式
            print(f"模型路径: {CONFIG['model_path']}")
            evaluation = evaluate_model(CONFIG['model_path'], CONFIG['metadata_path'])

            if evaluation['chiral_molecules'] > evaluation['total_generated'] * 0.5:
                print("\n✅ 评估通过: 模型能够生成高质量的手性分子!")
                return True
            else:
                print("\n❌ 评估失败: 模型生成的手性分子质量不足")
                return False

        else:
            print(f"错误: 不支持的运行模式 '{CONFIG['mode']}'")
            print("支持的模式: 'train', 'generate', 'evaluate'")
            return False

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数 - 直接运行配置好的任务"""
    success = run_chiral_model()

    if success:
        print("\n🎉 任务执行成功!")
    else:
        print("\n💥 任务执行失败!")

    return 0 if success else 1


if __name__ == "__main__":
    # 显示配置信息
    print("当前配置:")
    for key, value in CONFIG.items():
        if key == 'smiles_list' and len(value) > 2:
            print(f"  {key}: {value[:2]}... (共{len(value)}个)")
        else:
            print(f"  {key}: {value}")
    print()

    # 运行主程序
    exit(main())
