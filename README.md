# 手性感知分子生成模型

## 项目简介

手性感知的变分自编码器(VAE)模型，用于生成新的手性分子结构及其毒性标签。

## 核心特性

- **生成新的手性分子结构** (SMILES格式)
- **生成对应的毒性标签**
- **确保对映异构体具有不同的毒性**
- 基于学习的手性-活性关系

## 项目结构

```
ChiralToxicityVAE/
├── main.py                    # 主程序入口
├── chiral_features.py         # 手性感知特征提取
├── chiral_data.py            # 数据处理和增强
├── chiral_model.py           # 手性感知VAE模型
├── chiral_train.py           # 训练脚本
├── chiral_generate.py        # 生成和预测
└── README.md                 # 项目说明
```

## 使用方法

### 1. 训练模型
```python
CONFIG = {
    'mode': 'train',
    'data_file': 'data/your_data.csv',
    'output_dir': 'chiral_models'
}
```

### 2. 生成分子
```python
CONFIG = {
    'mode': 'generate',
    'model_path': 'chiral_models/best_chiral_model.pth',
    'metadata_path': 'chiral_data/chiral_metadata.pkl',
    'n_molecules': 1000,        # 生成分子数量
    'temperature': 1.0,         # 控制多样性
    'ensure_chirality': True,   # 确保手性
    'generate_pairs': True,     # 生成对映异构体对
    'output_dir': 'generated_molecules'
}
```

### 3. 评估模型
```python
CONFIG = {
    'mode': 'evaluate',
    'model_path': 'chiral_models/best_chiral_model.pth',
    'metadata_path': 'chiral_data/chiral_metadata.pkl'
}
```

### 4. 运行
```bash
python main.py
```

## 输出文件

生成模式会产生以下文件：
- `generated_chiral_molecules.csv`: 生成的分子和毒性数据
- `generation_stats.txt`: 生成统计信息

CSV文件包含：
- `smiles`: 生成的分子SMILES
- `enantiomer_smiles`: 对映异构体SMILES
- `*_toxicity`: 各终点毒性概率
- `*_binary`: 二进制毒性标签
- `toxicity_difference`: 对映异构体毒性差异

## 模型特点

真正的分子生成模型：
1. **生成全新的手性分子结构**
2. **为每个分子生成毒性标签**
3. **确保对映异构体有不同毒性**
4. 基于VAE的概率生成
