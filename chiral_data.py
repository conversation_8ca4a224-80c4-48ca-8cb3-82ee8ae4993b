"""
手性感知的数据处理模块
基于改进的特征提取方法处理分子数据
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset
from sklearn.preprocessing import StandardScaler
import pickle
import os
from typing import Tuple, List, Dict, Optional

from chiral_features import ChiralMolecularFeatureExtractor


class ChiralToxicityDataset(Dataset):
    """手性感知的毒性数据集"""
    
    def __init__(self, csv_file: str, feature_extractor: Optional[ChiralMolecularFeatureExtractor] = None):
        """
        初始化数据集
        
        Args:
            csv_file: CSV数据文件路径
            feature_extractor: 手性特征提取器
        """
        self.df = pd.read_csv(csv_file)
        self.feature_extractor = feature_extractor or ChiralMolecularFeatureExtractor()
        self.scaler = StandardScaler()
        
        # 检查数据中实际存在的毒性终点列名
        potential_endpoints = [
            'FishAT', 'acute_oral_toxicity', 'skin_sensitization', 'eye_irritation',
            'skin_irritation', 'mutagenicity'
        ]

        # 只保留数据中实际存在的列
        self.toxicity_endpoints = [col for col in potential_endpoints if col in self.df.columns]

        if not self.toxicity_endpoints:
            raise ValueError("数据中未找到任何毒性终点列")


        
        # 处理数据
        self._process_data()
    
    def _process_data(self):
        """处理数据：提取特征、处理毒性标签"""
        # 提取分子特征
        self.molecular_features = self._extract_molecular_features()

        # 处理毒性数据
        self.toxicity_data, self.toxicity_masks = self._process_toxicity_data()

        # 识别对映异构体对
        self.enantiomer_pairs = self._identify_enantiomer_pairs()
    
    def _extract_molecular_features(self) -> np.ndarray:
        """提取手性感知的分子特征"""
        features = []
        
        for i, smiles in enumerate(self.df['smiles']):
            feature_vector = self.feature_extractor.extract_features(smiles)
            features.append(feature_vector)
        
        features_array = np.array(features)
        
        # 标准化特征
        features_scaled = self.scaler.fit_transform(features_array)
        
        return features_scaled
    
    def _process_toxicity_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """处理毒性数据，创建标签和掩码"""
        toxicity_matrix = []
        mask_matrix = []

        for _, row in self.df.iterrows():
            toxicity_row = []
            mask_row = []

            for endpoint in self.toxicity_endpoints:
                if endpoint in self.df.columns:
                    value = row[endpoint]
                    if pd.notna(value):
                        # 确保值在[0,1]范围内
                        float_value = max(0.0, min(1.0, float(value)))

                        toxicity_row.append(float_value)
                        mask_row.append(1.0)
                    else:
                        toxicity_row.append(0.0)
                        mask_row.append(0.0)
                else:
                    toxicity_row.append(0.0)
                    mask_row.append(0.0)

            toxicity_matrix.append(toxicity_row)
            mask_matrix.append(mask_row)

        return np.array(toxicity_matrix), np.array(mask_matrix)
    
    def _identify_enantiomer_pairs(self) -> List[Tuple[int, int]]:
        """识别数据中的对映异构体对"""
        enantiomer_pairs = []
        processed_indices = set()
        
        for i, smiles_i in enumerate(self.df['smiles']):
            if i in processed_indices:
                continue
            
            for j, smiles_j in enumerate(self.df['smiles']):
                if j <= i or j in processed_indices:
                    continue
                
                if self._are_enantiomers(smiles_i, smiles_j):
                    enantiomer_pairs.append((i, j))
                    processed_indices.add(i)
                    processed_indices.add(j)
                    break
        
        return enantiomer_pairs
    
    def _are_enantiomers(self, smiles1: str, smiles2: str) -> bool:
        """判断两个SMILES是否为对映异构体"""
        try:
            from rdkit import Chem
            
            # 移除立体化学信息，比较分子骨架
            mol1 = Chem.MolFromSmiles(smiles1)
            mol2 = Chem.MolFromSmiles(smiles2)
            
            if mol1 is None or mol2 is None:
                return False
            
            # 移除立体化学信息
            Chem.RemoveStereochemistry(mol1)
            Chem.RemoveStereochemistry(mol2)
            
            # 比较分子骨架
            smiles1_no_stereo = Chem.MolToSmiles(mol1)
            smiles2_no_stereo = Chem.MolToSmiles(mol2)
            
            # 如果骨架相同但原始SMILES不同，则可能是对映异构体
            if smiles1_no_stereo == smiles2_no_stereo and smiles1 != smiles2:
                # 进一步检查是否有手性中心
                if '@' in smiles1 and '@' in smiles2:
                    return True
            
            return False
            
        except:
            return False
    
    def create_enantiomer_augmented_dataset(self) -> 'ChiralToxicityDataset':
        """创建包含对映异构体数据增强的数据集"""
        augmented_data = []
        
        for i, row in self.df.iterrows():
            # 添加原始数据
            augmented_data.append(row.to_dict())
            
            # 如果分子有手性中心，生成对映异构体
            smiles = row['smiles']
            if '@' in smiles:
                enantiomer_smiles = self._generate_enantiomer(smiles)
                if enantiomer_smiles != smiles:
                    # 创建对映异构体数据
                    enantiomer_row = row.to_dict().copy()
                    enantiomer_row['smiles'] = enantiomer_smiles
                    enantiomer_row['is_generated_enantiomer'] = True
                    enantiomer_row['original_index'] = i
                    
                    # 为对映异构体生成不同的毒性标签
                    enantiomer_row = self._generate_enantiomer_toxicity(enantiomer_row, row)
                    
                    augmented_data.append(enantiomer_row)
        
        # 创建新的DataFrame
        augmented_df = pd.DataFrame(augmented_data)
        
        # 保存到临时文件
        temp_file = 'temp_augmented_data.csv'
        augmented_df.to_csv(temp_file, index=False)
        
        # 创建新的数据集实例
        augmented_dataset = ChiralToxicityDataset(temp_file, self.feature_extractor)
        
        # 清理临时文件
        os.remove(temp_file)
        
        return augmented_dataset
    
    def _generate_enantiomer(self, smiles: str) -> str:
        """生成对映异构体SMILES"""
        enantiomer = smiles
        
        # 翻转手性中心
        temp_marker = "TEMP_MARKER"
        enantiomer = enantiomer.replace("@@H", temp_marker)
        enantiomer = enantiomer.replace("@H", "@@H")
        enantiomer = enantiomer.replace(temp_marker, "@H")
        
        # 处理其他手性标记
        if "@H" not in enantiomer:
            enantiomer = enantiomer.replace("@@]", "TEMP_BRACKET")
            enantiomer = enantiomer.replace("@]", "@@]")
            enantiomer = enantiomer.replace("TEMP_BRACKET", "@]")
            
            enantiomer = enantiomer.replace("@@)", "TEMP_PAREN")
            enantiomer = enantiomer.replace("@)", "@@)")
            enantiomer = enantiomer.replace("TEMP_PAREN", "@)")
        
        return enantiomer
    
    def _generate_enantiomer_toxicity(self, enantiomer_row: Dict, original_row: pd.Series) -> Dict:
        """为对映异构体生成不同的毒性标签"""
        # 基于生物学原理，对映异构体在不同终点可能有不同的活性
        toxicity_modifiers = {
            'FishAT': 0.3,                 # 鱼类急性毒性可能差异较大
            'acute_oral_toxicity': 0.7,    # 急性毒性可能相似
            'skin_sensitization': 0.3,     # 皮肤致敏可能差异较大
            'eye_irritation': 0.8,         # 眼刺激可能相似
            'skin_irritation': 0.6,        # 皮肤刺激中等差异
            'mutagenicity': 0.2            # 致突变性可能差异很大
        }

        for endpoint in self.toxicity_endpoints:
            if endpoint in enantiomer_row and pd.notna(original_row[endpoint]):
                original_value = float(original_row[endpoint])
                modifier = toxicity_modifiers.get(endpoint, 0.5)

                # 确保值在[0,1]范围内
                if original_value not in [0.0, 1.0]:
                    # 如果不是二进制值，先归一化
                    original_value = max(0.0, min(1.0, original_value))

                # 基于修饰因子调整毒性
                if np.random.random() > modifier:
                    # 翻转毒性标签
                    enantiomer_row[endpoint] = 1.0 - original_value
                else:
                    # 保持相似的毒性，添加小噪声
                    noise = np.random.normal(0, 0.05)  # 减小噪声
                    new_value = original_value + noise
                    enantiomer_row[endpoint] = max(0.0, min(1.0, new_value))

        return enantiomer_row
    
    def __len__(self):
        return len(self.df)
    
    def __getitem__(self, idx):
        return (
            torch.FloatTensor(self.molecular_features[idx]),
            torch.FloatTensor(self.toxicity_data[idx]),
            torch.FloatTensor(self.toxicity_masks[idx])
        )
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称"""
        return self.feature_extractor.feature_names
    
    def save_processed_data(self, output_dir: str = 'chiral_data'):
        """保存处理后的数据"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存数据集
        torch.save(self, os.path.join(output_dir, 'chiral_dataset.pt'))
        
        # 保存元数据
        metadata = {
            'feature_names': self.get_feature_names(),
            'toxicity_endpoints': self.toxicity_endpoints,
            'n_molecular_features': len(self.get_feature_names()),
            'n_toxicity_endpoints': len(self.toxicity_endpoints),
            'scaler': self.scaler,
            'enantiomer_pairs': self.enantiomer_pairs
        }
        
        with open(os.path.join(output_dir, 'chiral_metadata.pkl'), 'wb') as f:
            pickle.dump(metadata, f)
        
        return output_dir


def create_train_val_test_split(dataset: ChiralToxicityDataset, 
                               train_ratio: float = 0.7,
                               val_ratio: float = 0.15,
                               test_ratio: float = 0.15) -> Tuple[Dataset, Dataset, Dataset]:
    """创建训练、验证、测试数据集分割"""
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6
    
    n_samples = len(dataset)
    indices = np.random.permutation(n_samples)
    
    train_end = int(train_ratio * n_samples)
    val_end = train_end + int(val_ratio * n_samples)
    
    train_indices = indices[:train_end]
    val_indices = indices[train_end:val_end]
    test_indices = indices[val_end:]
    
    train_dataset = torch.utils.data.Subset(dataset, train_indices)
    val_dataset = torch.utils.data.Subset(dataset, val_indices)
    test_dataset = torch.utils.data.Subset(dataset, test_indices)
    
    return train_dataset, val_dataset, test_dataset



