"""
手性感知的分子特征提取模块
实现能够区分对映异构体的分子特征提取方法
"""

import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors, AllChem
from rdkit.Chem.rdchem import ChiralType
import re
from typing import List, Tuple, Dict, Optional


class ChiralMolecularFeatureExtractor:
    """手性感知的分子特征提取器"""
    
    def __init__(self):
        self.feature_names = [
            # 基础分子特征
            'molecular_weight', 'logp', 'tpsa', 'rotatable_bonds',
            'h_bond_donors', 'h_bond_acceptors', 'ring_count',
            
            # 手性特征
            'chiral_centers_count', 'r_centers_count', 's_centers_count',
            'chiral_volume', 'stereochemical_complexity',
            
            # 手性环境特征
            'chiral_carbon_env', 'chiral_nitrogen_env', 'chiral_sulfur_env',
            'aromatic_chiral_env', 'aliphatic_chiral_env',
            
            # 立体化学指纹
            'stereo_fingerprint_1', 'stereo_fingerprint_2', 'stereo_fingerprint_3',
            'stereo_fingerprint_4', 'stereo_fingerprint_5',
            
            # 分子形状和3D特征
            'asphericity', 'eccentricity', 'inertial_shape_factor',
            'radius_of_gyration', 'principal_moment_ratio'
        ]
    
    def extract_features(self, smiles: str) -> np.ndarray:
        """
        从SMILES提取手性感知的分子特征
        
        Args:
            smiles: 分子的SMILES字符串
            
        Returns:
            特征向量
        """
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return np.zeros(len(self.feature_names))
            
            # 添加氢原子用于准确的手性分析
            mol_with_h = Chem.AddHs(mol)
            
            features = []
            
            # 基础分子特征
            features.extend(self._extract_basic_features(mol))
            
            # 手性特征
            features.extend(self._extract_chiral_features(mol, smiles))
            
            # 手性环境特征
            features.extend(self._extract_chiral_environment_features(mol_with_h))
            
            # 立体化学指纹
            features.extend(self._extract_stereochemical_fingerprint(mol, smiles))
            
            # 3D形状特征（基于构象）
            features.extend(self._extract_3d_shape_features(mol_with_h))
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            return np.zeros(len(self.feature_names))
    
    def _extract_basic_features(self, mol) -> List[float]:
        """提取基础分子特征"""
        return [
            Descriptors.MolWt(mol),
            Descriptors.MolLogP(mol),
            Descriptors.TPSA(mol),
            Descriptors.NumRotatableBonds(mol),
            Descriptors.NumHDonors(mol),
            Descriptors.NumHAcceptors(mol),
            rdMolDescriptors.CalcNumRings(mol)
        ]
    
    def _extract_chiral_features(self, mol, smiles: str) -> List[float]:
        """提取手性特征"""
        # 识别手性中心
        chiral_centers = Chem.FindMolChiralCenters(mol, includeUnassigned=True)
        total_chiral = len(chiral_centers)
        
        # 统计R和S配置
        r_count, s_count = self._count_rs_configurations(smiles)
        
        # 计算手性体积（基于手性中心周围的原子）
        chiral_volume = self._calculate_chiral_volume(mol, chiral_centers)
        
        # 立体化学复杂度
        stereo_complexity = self._calculate_stereochemical_complexity(mol, chiral_centers)
        
        return [
            float(total_chiral),
            float(r_count),
            float(s_count),
            chiral_volume,
            stereo_complexity
        ]
    
    def _count_rs_configurations(self, smiles: str) -> Tuple[int, int]:
        """从SMILES字符串中统计R和S配置"""
        # 使用正则表达式识别手性标记
        r_pattern = r'@(?!@)'  # @ 但不是 @@
        s_pattern = r'@@'      # @@
        
        r_count = len(re.findall(r_pattern, smiles))
        s_count = len(re.findall(s_pattern, smiles))
        
        return r_count, s_count
    
    def _calculate_chiral_volume(self, mol, chiral_centers: List) -> float:
        """计算手性体积"""
        if not chiral_centers:
            return 0.0
        
        total_volume = 0.0
        for center_idx, _ in chiral_centers:
            atom = mol.GetAtomWithIdx(center_idx)
            # 基于原子的范德华半径估算局部体积
            neighbors = atom.GetNeighbors()
            local_volume = len(neighbors) * 10.0  # 简化计算
            total_volume += local_volume
        
        return total_volume / len(chiral_centers) if chiral_centers else 0.0
    
    def _calculate_stereochemical_complexity(self, mol, chiral_centers: List) -> float:
        """计算立体化学复杂度"""
        if not chiral_centers:
            return 0.0
        
        complexity = 0.0
        for center_idx, _ in chiral_centers:
            atom = mol.GetAtomWithIdx(center_idx)
            neighbors = atom.GetNeighbors()
            
            # 基于邻居原子的多样性计算复杂度
            neighbor_types = set([n.GetSymbol() for n in neighbors])
            complexity += len(neighbor_types) * len(neighbors)
        
        return complexity / len(chiral_centers) if chiral_centers else 0.0
    
    def _extract_chiral_environment_features(self, mol_with_h) -> List[float]:
        """提取手性中心的化学环境特征"""
        chiral_centers = Chem.FindMolChiralCenters(mol_with_h, includeUnassigned=True)
        
        # 初始化环境计数器
        carbon_env = nitrogen_env = sulfur_env = 0
        aromatic_env = aliphatic_env = 0
        
        for center_idx, _ in chiral_centers:
            atom = mol_with_h.GetAtomWithIdx(center_idx)
            
            # 统计手性中心的原子类型
            if atom.GetSymbol() == 'C':
                carbon_env += 1
            elif atom.GetSymbol() == 'N':
                nitrogen_env += 1
            elif atom.GetSymbol() == 'S':
                sulfur_env += 1
            
            # 统计芳香性环境
            if atom.GetIsAromatic():
                aromatic_env += 1
            else:
                aliphatic_env += 1
        
        return [
            float(carbon_env),
            float(nitrogen_env), 
            float(sulfur_env),
            float(aromatic_env),
            float(aliphatic_env)
        ]
    
    def _extract_stereochemical_fingerprint(self, mol, smiles: str) -> List[float]:
        """提取立体化学指纹"""
        # 基于SMILES中的立体化学模式创建指纹
        patterns = [
            r'C\[C@H\]',     # 常见手性模式1
            r'C\[C@@H\]',    # 常见手性模式2
            r'@H\]\(',       # 手性中心后的分支
            r'@@H\]\(',      # 反向手性中心后的分支
            r'@.*@'          # 多个手性中心
        ]
        
        fingerprint = []
        for pattern in patterns:
            matches = len(re.findall(pattern, smiles))
            fingerprint.append(float(matches))
        
        return fingerprint
    
    def _extract_3d_shape_features(self, mol_with_h) -> List[float]:
        """提取3D形状特征（需要构象生成）"""
        try:
            # 生成3D构象
            AllChem.EmbedMolecule(mol_with_h, randomSeed=42)
            AllChem.MMFFOptimizeMolecule(mol_with_h)
            
            # 计算3D描述符
            asphericity = rdMolDescriptors.CalcAsphericity(mol_with_h)
            eccentricity = rdMolDescriptors.CalcEccentricity(mol_with_h)
            inertial_shape = rdMolDescriptors.CalcInertialShapeFactor(mol_with_h)
            radius_gyration = rdMolDescriptors.CalcRadiusOfGyration(mol_with_h)
            
            # 主惯性矩比值
            principal_moments = rdMolDescriptors.CalcPrincipalAxesLengths(mol_with_h)
            moment_ratio = principal_moments[0] / principal_moments[2] if principal_moments[2] > 0 else 0.0
            
            return [asphericity, eccentricity, inertial_shape, radius_gyration, moment_ratio]
            
        except:
            # 如果3D构象生成失败，返回默认值
            return [0.0, 0.0, 0.0, 0.0, 1.0]



