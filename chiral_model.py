"""
手性感知的VAE模型
专门设计用于处理对映异构体的毒性预测
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional


class ChiralAttentionModule(nn.Module):
    """手性注意力模块，专门处理手性相关特征"""
    
    def __init__(self, feature_dim: int, chiral_feature_indices: list):
        super(ChiralAttentionModule, self).__init__()
        self.feature_dim = feature_dim
        self.chiral_indices = chiral_feature_indices
        self.chiral_dim = len(chiral_feature_indices)
        
        # 手性特征注意力网络
        self.chiral_attention = nn.Sequential(
            nn.Linear(self.chiral_dim, self.chiral_dim * 2),
            nn.ReLU(),
            nn.Linear(self.chiral_dim * 2, self.chiral_dim),
            nn.Sigmoid()
        )
        
        # 特征融合网络
        self.feature_fusion = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.LayerNorm(feature_dim),
            nn.ReLU()
        )
    
    def forward(self, molecular_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            molecular_features: 分子特征 [batch_size, feature_dim]
            
        Returns:
            增强的分子特征
        """
        # 提取手性特征
        chiral_features = molecular_features[:, self.chiral_indices]
        
        # 计算手性注意力权重
        chiral_attention_weights = self.chiral_attention(chiral_features)
        
        # 应用注意力权重到手性特征
        enhanced_chiral_features = chiral_features * chiral_attention_weights
        
        # 更新原始特征
        enhanced_features = molecular_features.clone()
        enhanced_features[:, self.chiral_indices] = enhanced_chiral_features
        
        # 特征融合
        output = self.feature_fusion(enhanced_features)
        
        return output


class ChiralConditioningModule(nn.Module):
    """手性条件模块，用于条件生成"""
    
    def __init__(self, latent_dim: int, chiral_condition_dim: int = 8):
        super(ChiralConditioningModule, self).__init__()
        self.latent_dim = latent_dim
        self.chiral_condition_dim = chiral_condition_dim
        
        # 手性条件编码器
        self.condition_encoder = nn.Sequential(
            nn.Linear(chiral_condition_dim, latent_dim // 2),
            nn.ReLU(),
            nn.Linear(latent_dim // 2, latent_dim // 4),
            nn.ReLU()
        )
        
        # 条件融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(latent_dim + latent_dim // 4, latent_dim),
            nn.LayerNorm(latent_dim),
            nn.ReLU()
        )
    
    def forward(self, latent_code: torch.Tensor, chiral_condition: torch.Tensor) -> torch.Tensor:
        """
        应用手性条件到潜在编码
        
        Args:
            latent_code: 潜在编码 [batch_size, latent_dim]
            chiral_condition: 手性条件 [batch_size, chiral_condition_dim]
            
        Returns:
            条件化的潜在编码
        """
        # 编码手性条件
        encoded_condition = self.condition_encoder(chiral_condition)
        
        # 融合潜在编码和手性条件
        combined = torch.cat([latent_code, encoded_condition], dim=1)
        conditioned_latent = self.fusion_network(combined)
        
        return conditioned_latent


class ChiralToxicityVAE(nn.Module):
    """手性感知的毒性预测VAE模型"""
    
    def __init__(self,
                 molecular_feature_dim: int = 23,
                 toxicity_dim: int = 1,
                 latent_dim: int = 64,
                 hidden_dims: list = [128, 64],
                 chiral_feature_indices: list = None):
        """
        初始化手性感知VAE
        
        Args:
            molecular_feature_dim: 分子特征维度
            toxicity_dim: 毒性标签维度
            latent_dim: 潜在空间维度
            hidden_dims: 隐藏层维度
            chiral_feature_indices: 手性特征在特征向量中的索引
        """
        super(ChiralToxicityVAE, self).__init__()
        
        self.molecular_feature_dim = molecular_feature_dim
        self.toxicity_dim = toxicity_dim
        self.latent_dim = latent_dim
        
        # 默认手性特征索引（基于chiral_features.py中的特征顺序）
        if chiral_feature_indices is None:
            # 手性相关特征的索引：chiral_centers_count, r_centers_count, s_centers_count等
            self.chiral_feature_indices = list(range(7, 17))  # 手性特征在特征向量中的位置
        else:
            self.chiral_feature_indices = chiral_feature_indices
        
        # 手性注意力模块
        self.chiral_attention = ChiralAttentionModule(
            molecular_feature_dim, self.chiral_feature_indices
        )
        
        # 编码器
        encoder_input_dim = molecular_feature_dim + toxicity_dim
        encoder_layers = []
        prev_dim = encoder_input_dim
        
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        self.fc_mu = nn.Linear(hidden_dims[-1], latent_dim)
        self.fc_logvar = nn.Linear(hidden_dims[-1], latent_dim)
        
        # 手性条件模块
        self.chiral_conditioning = ChiralConditioningModule(latent_dim)
        
        # 解码器
        decoder_input_dim = latent_dim + molecular_feature_dim
        decoder_layers = []
        prev_dim = decoder_input_dim
        
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        # 毒性预测头
        decoder_layers.extend([
            nn.Linear(prev_dim, toxicity_dim),
            nn.Sigmoid()
        ])
        
        self.decoder = nn.Sequential(*decoder_layers)
        
        # 手性-活性关系学习模块
        self.chiral_activity_predictor = nn.Sequential(
            nn.Linear(len(self.chiral_feature_indices) + latent_dim, hidden_dims[0]),
            nn.ReLU(),
            nn.Linear(hidden_dims[0], toxicity_dim),
            nn.Sigmoid()
        )
    
    def extract_chiral_condition(self, molecular_features: torch.Tensor) -> torch.Tensor:
        """从分子特征中提取手性条件"""
        chiral_features = molecular_features[:, self.chiral_feature_indices]
        
        # 创建手性条件向量
        batch_size = molecular_features.size(0)
        chiral_condition = torch.zeros(batch_size, 8, device=molecular_features.device)
        
        # 填充手性条件（前几个维度使用实际的手性特征）
        n_chiral_features = min(8, chiral_features.size(1))
        chiral_condition[:, :n_chiral_features] = chiral_features[:, :n_chiral_features]
        
        return chiral_condition
    
    def encode(self, molecular_features: torch.Tensor, 
               toxicity_data: torch.Tensor, 
               mask: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """编码器前向传播"""
        # 应用手性注意力
        enhanced_features = self.chiral_attention(molecular_features)
        
        # 掩码毒性数据
        masked_toxicity = toxicity_data * mask
        
        # 拼接特征
        x = torch.cat([enhanced_features, masked_toxicity], dim=1)
        
        # 编码
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        
        return mu, logvar
    
    def reparameterize(self, mu: torch.Tensor, logvar: torch.Tensor, 
                      temperature: float = 1.0) -> torch.Tensor:
        """重参数化技巧"""
        std = torch.exp(0.5 * logvar) * temperature
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z: torch.Tensor, molecular_features: torch.Tensor) -> torch.Tensor:
        """解码器前向传播"""
        # 应用手性注意力到分子特征
        enhanced_features = self.chiral_attention(molecular_features)
        
        # 提取手性条件
        chiral_condition = self.extract_chiral_condition(molecular_features)
        
        # 应用手性条件到潜在编码
        conditioned_z = self.chiral_conditioning(z, chiral_condition)
        
        # 解码
        x = torch.cat([conditioned_z, enhanced_features], dim=1)
        toxicity_pred = self.decoder(x)
        
        return toxicity_pred
    
    def predict_chiral_activity(self, molecular_features: torch.Tensor, 
                               z: torch.Tensor) -> torch.Tensor:
        """预测手性-活性关系"""
        chiral_features = molecular_features[:, self.chiral_feature_indices]
        combined = torch.cat([chiral_features, z], dim=1)
        chiral_activity = self.chiral_activity_predictor(combined)
        return chiral_activity
    
    def forward(self, molecular_features: torch.Tensor,
                toxicity_data: torch.Tensor,
                mask: torch.Tensor,
                temperature: float = 1.0) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """模型前向传播"""
        # 编码
        mu, logvar = self.encode(molecular_features, toxicity_data, mask)
        
        # 重参数化
        z = self.reparameterize(mu, logvar, temperature)
        
        # 解码
        recon_toxicity = self.decode(z, molecular_features)
        
        # 手性-活性预测
        chiral_activity = self.predict_chiral_activity(molecular_features, z)
        
        return recon_toxicity, mu, logvar, chiral_activity
    
    def generate_enantiomer_pair(self, molecular_features: torch.Tensor,
                                n_samples: int = 1,
                                temperature: float = 1.0) -> Tuple[torch.Tensor, torch.Tensor]:
        """生成对映异构体对的毒性预测"""
        self.eval()
        with torch.no_grad():
            batch_size = molecular_features.size(0)

            # 为R和S构型创建不同的手性条件
            r_condition = self.extract_chiral_condition(molecular_features)
            s_condition = r_condition.clone()

            # 修改手性条件以表示不同的立体化学配置
            # 这里简化处理：交换R和S中心的数量
            if r_condition.size(1) >= 3:
                s_condition[:, 1], s_condition[:, 2] = r_condition[:, 2], r_condition[:, 1]

            # 生成潜在编码
            z = torch.randn(batch_size * n_samples, self.latent_dim,
                          device=molecular_features.device) * temperature

            # 为R和S构型应用不同的条件
            z_r = self.chiral_conditioning(z, r_condition.repeat(n_samples, 1))
            z_s = self.chiral_conditioning(z, s_condition.repeat(n_samples, 1))

            # 解码生成毒性预测
            molecular_features_expanded = molecular_features.repeat(n_samples, 1)
            enhanced_features_r = self.chiral_attention(molecular_features_expanded)
            enhanced_features_s = self.chiral_attention(molecular_features_expanded)

            toxicity_r = self.decoder(torch.cat([z_r, enhanced_features_r], dim=1))
            toxicity_s = self.decoder(torch.cat([z_s, enhanced_features_s], dim=1))

            return toxicity_r, toxicity_s


def chiral_vae_loss_function(recon_toxicity: torch.Tensor,
                            target_toxicity: torch.Tensor,
                            mask: torch.Tensor,
                            mu: torch.Tensor,
                            logvar: torch.Tensor,
                            chiral_activity: torch.Tensor,
                            molecular_features: torch.Tensor,
                            beta: float = 1.0,
                            chiral_weight: float = 0.5) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    手性感知的VAE损失函数

    Args:
        recon_toxicity: 重构的毒性预测
        target_toxicity: 目标毒性标签
        mask: 数据掩码
        mu: 潜在空间均值
        logvar: 潜在空间对数方差
        chiral_activity: 手性-活性预测
        molecular_features: 分子特征
        beta: KL散度权重
        chiral_weight: 手性损失权重

    Returns:
        总损失, 重构损失, KL损失, 手性损失
    """
    # 确保输入在有效范围内
    recon_toxicity = torch.clamp(recon_toxicity, 1e-7, 1.0 - 1e-7)
    chiral_activity = torch.clamp(chiral_activity, 1e-7, 1.0 - 1e-7)
    target_toxicity = torch.clamp(target_toxicity, 0.0, 1.0)

    # 重构损失
    masked_recon = recon_toxicity * mask
    masked_target = target_toxicity * mask

    if mask.sum() > 0:
        recon_loss = F.binary_cross_entropy(
            masked_recon,
            masked_target,
            reduction='sum'
        ) / mask.sum()
    else:
        recon_loss = torch.tensor(0.0, device=recon_toxicity.device)

    # KL散度损失
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / mu.size(0)

    # 手性一致性损失
    masked_chiral = chiral_activity * mask

    if mask.sum() > 0:
        chiral_loss = F.binary_cross_entropy(
            masked_chiral,
            masked_target,
            reduction='sum'
        ) / mask.sum()
    else:
        chiral_loss = torch.tensor(0.0, device=chiral_activity.device)

    # 总损失
    total_loss = recon_loss + beta * kl_loss + chiral_weight * chiral_loss

    return total_loss, recon_loss, kl_loss, chiral_loss



