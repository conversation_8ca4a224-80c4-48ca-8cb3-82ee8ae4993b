"""
手性感知VAE模型的训练脚本
"""

import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
import os
import pickle
import time
from tqdm import tqdm
from typing import Dict, List, Tuple

from chiral_model import ChiralToxicityVAE, chiral_vae_loss_function
from chiral_data import ChiralToxicityDataset, create_train_val_test_split


class ChiralVAETrainer:
    """手性感知VAE训练器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device('cpu')  # 强制使用CPU避免CUDA问题
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.recon_losses = []
        self.kl_losses = []
        self.chiral_losses = []
        
        # 早停参数
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        

    
    def create_model(self, feature_dim: int, toxicity_dim: int):
        """创建手性感知VAE模型"""
        self.model = ChiralToxicityVAE(
            molecular_feature_dim=feature_dim,
            toxicity_dim=toxicity_dim,
            latent_dim=self.config['latent_dim'],
            hidden_dims=self.config['hidden_dims'],
            chiral_feature_indices=self.config.get('chiral_feature_indices', None)
        ).to(self.device)
        
        # 优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=self.config['scheduler_factor'],
            patience=self.config['scheduler_patience'],
            verbose=True
        )
        

    
    def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0
        total_recon = 0
        total_kl = 0
        total_chiral = 0
        
        # 动态调整损失权重
        beta = self._get_beta_schedule(epoch)
        chiral_weight = self._get_chiral_weight_schedule(epoch)
        temperature = self._get_temperature_schedule(epoch)
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
        for batch_idx, (molecular_features, toxicity_data, mask) in enumerate(pbar):
            molecular_features = molecular_features.to(self.device)
            toxicity_data = toxicity_data.to(self.device)
            mask = mask.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            recon_toxicity, mu, logvar, chiral_activity = self.model(
                molecular_features, toxicity_data, mask, temperature
            )
            
            # 计算损失
            loss, recon_loss, kl_loss, chiral_loss = chiral_vae_loss_function(
                recon_toxicity, toxicity_data, mask, mu, logvar, chiral_activity,
                molecular_features, beta=beta, chiral_weight=chiral_weight
            )
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 记录损失
            total_loss += loss.item()
            total_recon += recon_loss.item()
            total_kl += kl_loss.item()
            total_chiral += chiral_loss.item()
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Recon': f'{recon_loss.item():.4f}',
                'KL': f'{kl_loss.item():.4f}',
                'Chiral': f'{chiral_loss.item():.4f}',
                'Beta': f'{beta:.3f}',
                'ChiralW': f'{chiral_weight:.3f}'
            })
        
        return {
            'total_loss': total_loss / len(train_loader),
            'recon_loss': total_recon / len(train_loader),
            'kl_loss': total_kl / len(train_loader),
            'chiral_loss': total_chiral / len(train_loader),
            'beta': beta,
            'chiral_weight': chiral_weight,
            'temperature': temperature
        }
    
    def validate_epoch(self, val_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0
        total_recon = 0
        total_kl = 0
        total_chiral = 0
        
        beta = self._get_beta_schedule(epoch)
        chiral_weight = self._get_chiral_weight_schedule(epoch)
        
        with torch.no_grad():
            for molecular_features, toxicity_data, mask in val_loader:
                molecular_features = molecular_features.to(self.device)
                toxicity_data = toxicity_data.to(self.device)
                mask = mask.to(self.device)
                
                recon_toxicity, mu, logvar, chiral_activity = self.model(
                    molecular_features, toxicity_data, mask
                )
                
                loss, recon_loss, kl_loss, chiral_loss = chiral_vae_loss_function(
                    recon_toxicity, toxicity_data, mask, mu, logvar, chiral_activity,
                    molecular_features, beta=beta, chiral_weight=chiral_weight
                )
                
                total_loss += loss.item()
                total_recon += recon_loss.item()
                total_kl += kl_loss.item()
                total_chiral += chiral_loss.item()
        
        return {
            'total_loss': total_loss / len(val_loader),
            'recon_loss': total_recon / len(val_loader),
            'kl_loss': total_kl / len(val_loader),
            'chiral_loss': total_chiral / len(val_loader)
        }
    
    def _get_beta_schedule(self, epoch: int) -> float:
        """获取KL散度权重的调度"""
        # 周期性beta调度，有助于避免模式坍塌
        cycle_length = self.config.get('beta_cycle_length', 50)
        beta_min = self.config.get('beta_min', 0.1)
        beta_max = self.config.get('beta_max', 1.0)
        
        cycle_position = (epoch % cycle_length) / cycle_length
        beta = beta_min + (beta_max - beta_min) * (1 + np.cos(np.pi * cycle_position)) / 2
        
        return beta
    
    def _get_chiral_weight_schedule(self, epoch: int) -> float:
        """获取手性损失权重的调度"""
        # 逐渐增加手性损失的权重
        max_weight = self.config.get('max_chiral_weight', 1.0)
        warmup_epochs = self.config.get('chiral_warmup_epochs', 20)
        
        if epoch < warmup_epochs:
            return max_weight * (epoch / warmup_epochs)
        else:
            return max_weight
    
    def _get_temperature_schedule(self, epoch: int) -> float:
        """获取温度调度"""
        # 逐渐降低温度，增加生成的确定性
        initial_temp = self.config.get('initial_temperature', 1.5)
        final_temp = self.config.get('final_temperature', 0.8)
        decay_epochs = self.config.get('temperature_decay_epochs', 100)
        
        if epoch < decay_epochs:
            progress = epoch / decay_epochs
            return initial_temp * (1 - progress) + final_temp * progress
        else:
            return final_temp
    
    def evaluate_enantiomer_generation(self, val_loader: DataLoader) -> Dict[str, float]:
        """评估对映异构体生成的差异性"""
        self.model.eval()
        
        total_differences = []
        significant_differences = 0
        total_pairs = 0
        
        with torch.no_grad():
            for molecular_features, _, _ in val_loader:
                molecular_features = molecular_features.to(self.device)
                
                # 生成对映异构体对
                toxicity_r, toxicity_s = self.model.generate_enantiomer_pair(
                    molecular_features, n_samples=1
                )
                
                # 计算差异
                differences = torch.abs(toxicity_r - toxicity_s).mean(dim=1)
                total_differences.extend(differences.cpu().numpy())
                
                # 统计显著差异（差异 > 0.1）
                significant_differences += (differences > 0.1).sum().item()
                total_pairs += differences.size(0)
        
        avg_difference = np.mean(total_differences)
        significant_ratio = significant_differences / total_pairs if total_pairs > 0 else 0
        
        return {
            'avg_enantiomer_difference': avg_difference,
            'significant_difference_ratio': significant_ratio,
            'total_pairs_evaluated': total_pairs
        }
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader):
        """主训练循环"""
        print("开始训练手性感知VAE模型...")
        
        for epoch in range(self.config['epochs']):
            start_time = time.time()
            
            # 训练
            train_metrics = self.train_epoch(train_loader, epoch)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader, epoch)
            
            # 记录损失
            self.train_losses.append(train_metrics['total_loss'])
            self.val_losses.append(val_metrics['total_loss'])
            self.recon_losses.append(train_metrics['recon_loss'])
            self.kl_losses.append(train_metrics['kl_loss'])
            self.chiral_losses.append(train_metrics['chiral_loss'])
            
            # 评估对映异构体生成（每10个epoch）
            if epoch % 10 == 0:
                enantiomer_metrics = self.evaluate_enantiomer_generation(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_metrics['total_loss'])
            
            # 早停检查
            if val_metrics['total_loss'] < self.best_val_loss:
                self.best_val_loss = val_metrics['total_loss']
                self.patience_counter = 0
                self.save_checkpoint(epoch, is_best=True)
            else:
                self.patience_counter += 1
            
            # 打印进度
            epoch_time = time.time() - start_time
            print(f"Epoch {epoch+1}/{self.config['epochs']} - "
                  f"Train Loss: {train_metrics['total_loss']:.4f}, "
                  f"Val Loss: {val_metrics['total_loss']:.4f}, "
                  f"Chiral Loss: {train_metrics['chiral_loss']:.4f}, "
                  f"Time: {epoch_time:.1f}s")
            
            # 早停
            if self.patience_counter >= self.config['patience']:
                break

        self.plot_training_curves()
        return self.model
    
    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """保存模型检查点"""
        os.makedirs(self.config['output_dir'], exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_val_loss': self.best_val_loss,
            'config': self.config,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }
        
        filename = 'best_chiral_model.pth' if is_best else f'chiral_model_epoch_{epoch}.pth'
        filepath = os.path.join(self.config['output_dir'], filename)
        torch.save(checkpoint, filepath)
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 总损失
        axes[0, 0].plot(self.train_losses, label='Train')
        axes[0, 0].plot(self.val_losses, label='Validation')
        axes[0, 0].set_title('Total Loss')
        axes[0, 0].legend()
        
        # 重构损失
        axes[0, 1].plot(self.recon_losses, label='Reconstruction')
        axes[0, 1].set_title('Reconstruction Loss')
        axes[0, 1].legend()
        
        # KL损失
        axes[1, 0].plot(self.kl_losses, label='KL Divergence')
        axes[1, 0].set_title('KL Divergence Loss')
        axes[1, 0].legend()
        
        # 手性损失
        axes[1, 1].plot(self.chiral_losses, label='Chiral')
        axes[1, 1].set_title('Chiral Loss')
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.config['output_dir'], 'training_curves.png'))
        plt.show()


# 训练配置
CHIRAL_CONFIG = {
    'latent_dim': 32,
    'hidden_dims': [128, 64],
    'learning_rate': 1e-4,
    'weight_decay': 1e-6,
    'batch_size': 16,
    'epochs': 50,
    'patience': 15,
    
    # 损失权重调度
    'beta_cycle_length': 20,
    'beta_min': 0.01,
    'beta_max': 0.5,
    'max_chiral_weight': 0.3,
    'chiral_warmup_epochs': 10,
    
    # 温度调度
    'initial_temperature': 1.0,
    'final_temperature': 0.9,
    'temperature_decay_epochs': 30,
    
    # 学习率调度
    'scheduler_factor': 0.8,
    'scheduler_patience': 5,
    
    'output_dir': 'chiral_models'
}



